/*
 * BLE UDP Broadcast Module
 * 
 * 用于将BLE从机数据通过UDP广播发送给连接到WiFi AP的PC客户端
 * 使用定向广播(*************)避免多网卡路由问题
 */

#ifndef BLE_UDP_BROADCAST_H
#define BLE_UDP_BROADCAST_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief UDP广播配置参数
     */
    #define UDP_BROADCAST_PORT 8888                    ///< UDP广播端口
    #define UDP_BROADCAST_IP "*************"          ///< 定向广播地址(ESP32 AP网段)
    #define UDP_BROADCAST_MAX_RETRY 3                  ///< 发送失败最大重试次数
    #define UDP_BROADCAST_TIMEOUT_MS 100               ///< 发送超时时间(毫秒)

    /**
     * @brief 初始化UDP广播功能
     * 
     * 创建UDP socket并配置广播选项
     * 注意：需要在WiFi AP启动后调用
     * 
     * @return 
     *     - ESP_OK: 初始化成功
     *     - ESP_FAIL: 初始化失败
     *     - ESP_ERR_NO_MEM: 内存不足
     */
    esp_err_t ble_udp_broadcast_init(void);

    /**
     * @brief 通过UDP广播发送BLE数据
     * 
     * 将BLE从机数据以原始二进制格式广播到网段内的所有PC客户端
     * 使用定向广播(*************)确保只在ESP32 AP网段内传播
     * 
     * @param data 要广播的数据指针
     * @param length 数据长度(字节数)
     * 
     * @return 
     *     - ESP_OK: 发送成功
     *     - ESP_FAIL: 发送失败
     *     - ESP_ERR_INVALID_ARG: 参数无效
     *     - ESP_ERR_INVALID_STATE: UDP未初始化
     */
    esp_err_t ble_udp_broadcast_send(const uint8_t *data, size_t length);

    /**
     * @brief 获取UDP广播统计信息
     * 
     * @param total_sent 总发送次数(可为NULL)
     * @param total_failed 总失败次数(可为NULL)
     * @param last_error 最后一次错误码(可为NULL)
     * 
     * @return ESP_OK
     */
    esp_err_t ble_udp_broadcast_get_stats(uint32_t *total_sent, uint32_t *total_failed, int *last_error);

    /**
     * @brief 启用或禁用UDP广播功能
     * 
     * @param enable true=启用, false=禁用
     * 
     * @return ESP_OK
     */
    esp_err_t ble_udp_broadcast_enable(bool enable);

    /**
     * @brief 检查UDP广播是否已启用
     * 
     * @return true=已启用, false=已禁用
     */
    bool ble_udp_broadcast_is_enabled(void);

    /**
     * @brief 反初始化UDP广播功能
     * 
     * 关闭socket并释放资源
     * 
     * @return ESP_OK
     */
    esp_err_t ble_udp_broadcast_deinit(void);

#ifdef __cplusplus
}
#endif

#endif // BLE_UDP_BROADCAST_H
