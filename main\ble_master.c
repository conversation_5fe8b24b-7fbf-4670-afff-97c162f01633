
#include "ble_master.h"
#include "ble_uart.h"
#include "check_crc16.h"

#define BLE_PEER_NAME "BLE-SLAVE"
// #define BLE_PEER_MAX_NUM (MYNEWT_VAL(BLE_MAX_CONNECTIONS) - 1)
#define BLE_PEER_MAX_NUM (4)
#define BLE_PREF_EVT_LEN_MS (10)                                       // 增加事件长度以改善WiFi共存
#define BLE_PREF_CONN_ITVL_MS (BLE_PEER_MAX_NUM * BLE_PREF_EVT_LEN_MS) // 现在是40ms

/*
 * BLE信道映射优化 - 避免与WiFi信道13 (2472MHz)冲突
 * BLE使用40个数据信道(0-36)和3个广播信道(37,38,39)
 * WiFi信道13 (2472MHz)主要与BLE信道35,36,37冲突
 * 我们将禁用这些冲突信道以减少干扰
 */
#define BLE_CHANNEL_MAP_AVOID_WIFI13 0x1FFFFFFFFF // 禁用信道35,36,37 (保留0-34)
#define BLE_PEER_READ_INTERVAL (500 * 1000)
//
static const char *TAG = "ESP_MULTI_CONN_CENT";

static const ble_uuid_t *remote_svc_uuid = BLE_UUID16_DECLARE(0x00FF);
static uint8_t s_ble_multi_conn_num = 0;
// 定时器句柄，用于定期读取从机数据
static esp_timer_handle_t periodic_read_timer;

// 函数声明
void ble_store_config_init(void);
static void ble_cent_scan(void);
static void ble_cent_connect(void *disc);

// 从机特征值UUID定义 - 与从机保持一致
static const ble_uuid_t *slave_response_char_uuid = BLE_UUID16_DECLARE(0xFF01);
static const ble_uuid_t *slave_config_char_uuid = BLE_UUID16_DECLARE(0xFF02);

// UART协议定义
#define UART_FRAME_HEADER_1 0x80
#define UART_FRAME_HEADER_2 0x01
#define UART_FRAME_MIN_LENGTH 8 // 帧头(2) + 设备ID(2) + 采样率(2) + CRC16(2)

/**
 * @brief UART接收数据回调函数
 *
 * 解析UART接收到的配置命令并执行相应操作
 *
 * @param data 接收到的数据指针
 * @param length 数据长度
 */
static void uart_recv_callback(const uint8_t *data, size_t length)
{
    ESP_LOGI(TAG, "UART received %zu bytes", length);
    ESP_LOG_BUFFER_HEX(TAG, data, length);

    /*
    // 检查最小帧长度
    if (length < UART_FRAME_MIN_LENGTH)
    {
        ESP_LOGW(TAG, "UART frame too short: %zu bytes, expected at least %d", length, UART_FRAME_MIN_LENGTH);
        ESP_LOGI(TAG, "Data content (as text): %.*s", (int)length, (char *)data);
        return;
    }

    // 检查帧头
    if (data[0] != UART_FRAME_HEADER_1 || data[1] != UART_FRAME_HEADER_2)
    {
        ESP_LOGW(TAG, "Invalid UART frame header: 0x%02X 0x%02X, expected 0x%02X 0x%02X",
                 data[0], data[1], UART_FRAME_HEADER_1, UART_FRAME_HEADER_2);
        ESP_LOGI(TAG, "Data content (as text): %.*s", (int)length, (char *)data);
        return;
    }

    // 解析设备ID（小端序）
    uint16_t device_id = data[2] | (data[3] << 8);

    // 解析采样频率（小端序）
    uint16_t sample_rate_raw = data[4] | (data[5] << 8);
    uint8_t sample_rate = (uint8_t)sample_rate_raw;

    // 验证CRC16（小端序，CRC16位于帧尾）
    if (!verify_crc16(data, length, length - 2, true))
    {
        ESP_LOGW(TAG, "CRC16 verification failed");
        return;
    }

    ESP_LOGI(TAG, "Valid UART config command: Device ID=%d, Sample Rate=%d Hz", device_id, sample_rate);

    // 配置指定从机的采样频率
    esp_err_t ret = ble_master_config_slave(device_id, sample_rate);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure slave device %d: %s", device_id, esp_err_to_name(ret));
    }
    else
    {
        ESP_LOGI(TAG, "Successfully configured slave device %d with sample rate %d Hz", device_id, sample_rate);
    }
    */
}

/**
 * 读取特征值完成回调函数
 *
 * @param conn_handle 连接句柄
 * @param error       错误码 (0=成功)
 * @param attr        属性句柄
 * @param arg         用户参数
 */
static int
ble_cent_read_chr_cb(uint16_t conn_handle, const struct ble_gatt_error *error,
                     struct ble_gatt_attr *attr, void *arg)
{
    if (error->status == 0)
    {
        // ESP_LOGI(TAG, "Read success from conn_handle=%d, data_len=%d",conn_handle, attr->om->om_len);

        // 检查数据长度 - 支持14字节和20字节两种格式
        if (attr->om->om_len == sizeof(struct gatts_slave_response) || attr->om->om_len == 20)
        {

            //
            struct gatts_slave_response *slave_data = (struct gatts_slave_response *)attr->om->om_data;

            ESP_LOGI(TAG, "从机数据(20字节) - 连接句柄: %d, 设备ID: %d, MAC: %02X:%02X:%02X:%02X:%02X:%02X, 电池: %d%%, 温度: %.1f°C, 压力: %.1f kPa",
                     conn_handle, slave_data->device_id,
                     slave_data->device_mac[0], slave_data->device_mac[1], slave_data->device_mac[2],
                     slave_data->device_mac[3], slave_data->device_mac[4], slave_data->device_mac[5],
                     slave_data->battery_level, slave_data->temperature, slave_data->pressure);

            // 通过UART转发从机原始数据
            ble_uart_send((const uint8_t *)slave_data, sizeof(struct gatts_slave_response));

            // 通过UDP广播发送从机数据到PC客户端
            esp_err_t udp_ret = ble_udp_broadcast_send((const uint8_t *)slave_data, sizeof(struct gatts_slave_response));
            if (udp_ret != ESP_OK)
            {
                ESP_LOGD(TAG, "UDP broadcast failed: %s", esp_err_to_name(udp_ret));
            }
        }
        else
        {
            ESP_LOGW(TAG, "数据长度不匹配，期望: %d , 实际: %d", sizeof(struct gatts_slave_response), attr->om->om_len);
            // 打印原始数据（十六进制）用于调试
            ESP_LOG_BUFFER_HEX(TAG, attr->om->om_data, attr->om->om_len);

            // 即使长度不匹配，也尝试转发原始数据
            ble_uart_send(attr->om->om_data, attr->om->om_len);

            // 通过UDP广播发送原始数据到PC客户端
            esp_err_t udp_ret = ble_udp_broadcast_send(attr->om->om_data, attr->om->om_len);
            if (udp_ret != ESP_OK)
            {
                ESP_LOGD(TAG, "UDP broadcast failed for raw data: %s", esp_err_to_name(udp_ret));
            }
        }
    }
    else
    {
        ESP_LOGE(TAG, "Read failed from conn_handle=%d, error=%d", conn_handle, error->status);
    }
    return 0;
}

/**
 * 定时器回调函数 - 每秒读取所有从机的数据
 */
static void
periodic_read_timer_cb(void *arg)
{
    // ESP_LOGI(TAG, "Reading data from all connected slaves (%d connections)",s_ble_multi_conn_num);

    if (s_ble_multi_conn_num == 0)
    {
        ESP_LOGD(TAG, "No connected slaves to read from");
        return;
    }

    // 遍历所有连接的设备并读取数据
    // 使用peer管理来查找所有活跃连接
    int rc;

    // 遍历可能的连接句柄范围 (从0开始，因为看到连接句柄是0)
    for (uint16_t conn_handle = 0; conn_handle <= BLE_PEER_MAX_NUM; conn_handle++)
    {
        // 查找对应的peer来验证连接是否存在
        const struct peer *peer = peer_find(conn_handle);
        if (peer == NULL)
        {
            continue; // 该连接句柄没有对应的peer，跳过
        }

        // 查找从机响应特征值
        const struct peer_chr *chr = peer_chr_find_uuid(peer, remote_svc_uuid, slave_response_char_uuid);
        if (chr == NULL)
        {
            // 打印所有可用的服务和特征值用于调试
            const struct peer_svc *svc;
            SLIST_FOREACH(svc, &peer->svcs, next)
            {
                ESP_LOGW(TAG, "Service UUID: 0x%04x", ble_uuid_u16(&svc->svc.uuid.u));
                const struct peer_chr *debug_chr;
                SLIST_FOREACH(debug_chr, &svc->chrs, next)
                {
                    ESP_LOGW(TAG, "  Char UUID: 0x%04x, handle: %d", ble_uuid_u16(&debug_chr->chr.uuid.u), debug_chr->chr.val_handle);
                }
            }
            continue;
        }

        // 读取特征值
        rc = ble_gattc_read(conn_handle, chr->chr.val_handle, ble_cent_read_chr_cb, NULL);
        if (rc != 0)
        {
            ESP_LOGE(TAG, "Failed to read characteristic from conn_handle=%d, rc=%d", conn_handle, rc);
        }
    }
}

/**
 * GATT 服务发现完成回调函数
 *
 * 当对指定连接设备的服务发现过程完成时调用此函数。
 * 服务发现是在连接建立后进行的，用于获取对方设备支持的
 * 服务、特征值和描述符列表。
 *
 * @param peer   对等设备信息结构体
 * @param status 服务发现状态 (0=成功, 非0=失败)
 * @param arg    用户自定义参数（未使用）
 */
static void
ble_cent_on_disc_complete(const struct peer *peer, int status, void *arg)
{
    if (status != 0)
    {
        /* Service discovery failed.  Terminate the connection. */
        ESP_LOGE(TAG, "Error: Service discovery failed; status=%d conn_handle=%d", status,
                 peer->conn_handle);
        ble_gap_terminate(peer->conn_handle, BLE_ERR_REM_USER_CONN_TERM);
        return;
    }

    // 现在知道了对方设备支持的所有服务、特征值、描述符
    // 可以开始进行数据读写操作
    ESP_LOGD(TAG, "Service discovery complete; status=%d conn_handle=%d\n", status,
             peer->conn_handle);

    // 如果这是第一个连接，启动定时器
    if (s_ble_multi_conn_num == 1)
    {
        ESP_LOGI(TAG, "Starting periodic read timer (5 second interval)");

        // 定时器配置
        esp_timer_create_args_t timer_config = {
            .callback = periodic_read_timer_cb, // 回调函数
            .arg = NULL,
            .name = "periodic_read"};

        // 创建定时器
        esp_err_t ret = esp_timer_create(&timer_config, &periodic_read_timer);
        if (ret == ESP_OK)
        {
            // 启动定时器，每1秒触发一次 (1000000 微秒)
            esp_timer_start_periodic(periodic_read_timer, BLE_PEER_READ_INTERVAL);
            ESP_LOGI(TAG, "Periodic read timer started successfully");
        }
        else
        {
            ESP_LOGE(TAG, "Failed to create periodic read timer: %s", esp_err_to_name(ret));
        }
    }
}

/**
 * BLE GAP 事件回调函数 - 中心设备专用
 *
 * 当 NimBLE 协议栈发生 GAP 事件时，会调用此回调函数。
 * 此函数专门用于处理中心设备的各种 BLE 事件。
 *
 * @param event  触发的事件结构体，包含事件类型和相关数据
 * @param arg    应用程序自定义参数（本例中未使用）
 *
 * @return       0: 成功处理事件
 *               非0: 处理失败，具体语义取决于事件类型
 *
 * 处理的事件类型：
 * - BLE_GAP_EVENT_EXT_DISC: 扩展发现事件（发现广播设备）
 * - BLE_GAP_EVENT_CONNECT: 连接建立事件
 * - BLE_GAP_EVENT_DISCONNECT: 连接断开事件
 * - BLE_GAP_EVENT_DISC_COMPLETE: 扫描完成事件
 * - BLE_GAP_EVENT_TRANSMIT_POWER: 发射功率变化事件（可选）
 * - BLE_GAP_EVENT_PATHLOSS_THRESHOLD: 路径损耗阈值事件（可选）
 */
static int
ble_cent_client_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;
    int rc;

    switch (event->type)
    {
    // BLE 扩展发现事件,每发现一个设备就触发一次该事件
    // 这是 BLE 5.0 引入的扩展广播功能的一部分,可以处理更大的广播数据包
    // 传统的 BLE_GAP_EVENT_DISC：仅支持传统的 31 字节广播数据
    case BLE_GAP_EVENT_EXT_DISC:

        // 获取从机的广播数据(广播数据的原始字节,广播数据的长度
        rc = ble_hs_adv_parse_fields(&fields, event->ext_disc.data, event->ext_disc.length_data);

        // 过滤是否是我们要连接的设备
        if ((rc == 0) && fields.name && (fields.name_len >= strlen(BLE_PEER_NAME)) &&
            !strncmp((const char *)fields.name, BLE_PEER_NAME, strlen(BLE_PEER_NAME)))
        {
            // event->ext_disc 设备完整信息的结构体,包含地址、广播数据等
            ble_cent_connect(&event->ext_disc);
        }

        return 0;

    // 当连接成功建立或连接尝试失败时触发
    case BLE_GAP_EVENT_CONNECT:

        // 0=成功  非0=失败
        if (event->connect.status == 0)
        {
            // 从设备数++
            ESP_LOGI(TAG, "Connection established. Handle:%d, Total:%d", event->connect.conn_handle, ++s_ble_multi_conn_num);

            // 连接成后功, 保存连接句柄
            // peer_add() 不是协议栈的 API，而是来自 nimble_central_utils 组件的应用层工具函数
            // peer_init() - 初始化 peer 管理数据结构
            // peer_add() - 添加新的连接
            // peer_delete() - 删除连接
            rc = peer_add(event->connect.conn_handle);
            if (rc != 0)
            {
                ESP_LOGE(TAG, "Failed to add peer; rc=%d\n", rc);
            }
            else
            {
                // BLE 设备连接成功后，主机需要了解外设提供了哪些服务和特征值
                // 这个过程叫做 GATT 服务发现 (Service Discovery)
                // 类似于"问对方：你能提供什么功能？"
                rc = peer_disc_svc_by_uuid(event->connect.conn_handle, remote_svc_uuid, ble_cent_on_disc_complete, NULL);
                if (rc != 0)
                {
                    ESP_LOGE(TAG, "Failed to discover services; rc=%d\n", rc);
                }
            }
        }
        else
        {
            /* Connection attempt failed; resume scanning. */
            ESP_LOGE(TAG, "Central: Connection failed; status=0x%x\n", event->connect.status);
        }
        ble_cent_scan();
        return 0;

    // 当 BLE 连接被断开时触发
    // 主动断开连接时
    // 连接超时断开时
    // 设备移出范围导致连接丢失时
    // 连接参数协商失败时
    case BLE_GAP_EVENT_DISCONNECT:

        // 显示断开连接的详细信息
        // BLE_ERR_REM_USER_CONN_TERM：远程用户主动断开
        // BLE_ERR_CONN_TIMEOUT：连接超时
        // BLE_ERR_CONN_TERM_LOCAL：本地主动断开
        print_conn_desc(&event->disconnect.conn);

        // 从连接管理列表中删除该连接
        peer_delete(event->disconnect.conn.conn_handle);

        ESP_LOGI(TAG, "Central disconnected; Handle:%d, Reason=%d, Total:%d",
                 // 断开的连接句柄
                 event->disconnect.conn.conn_handle,
                 // 断开原因代码
                 event->disconnect.reason,
                 // 减少连接计数
                 --s_ble_multi_conn_num);

        // 如果没有连接了，停止定时器
        if (s_ble_multi_conn_num == 0 && periodic_read_timer != NULL)
        {
            ESP_LOGI(TAG, "No more connections, stopping periodic read timer");
            esp_timer_stop(periodic_read_timer);
            esp_timer_delete(periodic_read_timer);
            periodic_read_timer = NULL;
        }

        // 继续扫描寻找新设备
        ble_cent_scan();
        return 0;

    // 当 BLE 设备发现（扫描）过程结束时触发
    case BLE_GAP_EVENT_DISC_COMPLETE:
        ESP_LOGI(TAG, "discovery complete; reason=%d\n", event->disc_complete.reason);
        return 0;

        // 只有在启用功率控制功能时才编译这段代码, 这是一个可选的 BLE 高级特性
        // 主要用于优化无线通信的性能和效率。
        // 在项目配置中搜BLE_POWER_CONTROL打开/关闭
#if MYNEWT_VAL(BLE_POWER_CONTROL)
    // 当设备的发射功率发生变化时触发
    // 用途：监控和调整无线信号强度
    case BLE_GAP_EVENT_TRANSMIT_POWER:
        ESP_LOGI(TAG, "Transmit power event : status=%d conn_handle=%d reason=%d phy=%d "
                      "power_level=%x power_level_flag=%d delta=%d",
                 event->transmit_power.status,
                 event->transmit_power.conn_handle, event->transmit_power.reason,
                 event->transmit_power.phy, event->transmit_power.transmit_power_level,
                 event->transmit_power.transmit_power_level_flag, event->transmit_power.delta);
        return 0;

    // 当信号路径损耗超过设定阈值时触发
    // 用途：检测设备距离变化，优化连接质量
    case BLE_GAP_EVENT_PATHLOSS_THRESHOLD:
        ESP_LOGI(TAG, "Pathloss threshold event : conn_handle=%d current path loss=%d "
                      "zone_entered =%d",
                 event->pathloss_threshold.conn_handle,
                 event->pathloss_threshold.current_path_loss, event->pathloss_threshold.zone_entered);
        return 0;
#endif

    default:
        return 0;
    }
}

/**
 * Initiates the GAP general discovery procedure.
 */
static void
ble_cent_scan(void)
{
    int rc;

    // 如果已经启动扫描，就不再重复启动
    if (ble_gap_disc_active())
    {
        return;
    }

    // 准备两种扫描参数（分别对应不同 PHY）

    // uncoded PHY（1M/2M）：普通速率；
    // 传输速率高，适合近距离、稳定连接；
    // 通常用于所有主流 BLE 应用。
    struct ble_gap_ext_disc_params uncoded_disc_params;

    /*
        被动扫描（passive = 1） 只听不说，设备只接收对方的广播包，不发任何回应。

        优点：
        功耗低；
        对方设备不会知道你在扫描它；
        不会引发连接负载；

        缺点：
        收不到设备的 scan response 数据（比如更多的广播字段）；
        只能看到广播包里默认携带的内容（name、UUID 限长度）；

        主动扫描（passive = 0） 听完还问一句：“还有更多信息吗？”
        中心设备会向广播设备发出 Scan Request，广播设备再回一个 Scan Response 包。

        优点：
        可以获取更完整的广播字段，比如完整设备名、服务列表等；
        适合需要判断更多广播条件的场景；

        缺点：
        更耗电；
        会引起对方设备注意你在扫描；
        广播设备必须支持 Scan Response（不是所有都支持）；

        结论：
        连接前只看名字等简单信息 → 用被动扫描即可；
        你需要完整广播信息才能决定连不连 → 用主动扫描；

    */
    // 这里选择被动扫描
    uncoded_disc_params.passive = 1;
    // 扫描间隔2000ms (进一步增加间隔以减少与WiFi信道13冲突)
    uncoded_disc_params.itvl = BLE_GAP_SCAN_ITVL_MS(2000);
    // 每次扫描80ms (进一步减少扫描窗口)
    uncoded_disc_params.window = BLE_GAP_SCAN_WIN_MS(80);

    // coded PHY（125k/500k）：远距离传输。
    // BLE 5 引入的新特性；
    // 速率低（125kbps / 500kbps），但抗干扰强，适合远距离（可达 > 200m）；
    // 广播设备也必须支持 Coded PHY 才能被你收到；
    // 有的 ESP32 模块（比如 ESP32-C3）支持，有的老模块不支持；
    // 扫描时间窗要设得更长，比如 300ms+，否则很容易漏扫；
    struct ble_gap_ext_disc_params coded_disc_params;

    // 参数同上 - 优化共存性能，避免WiFi信道13干扰
    coded_disc_params.passive = 1;
    coded_disc_params.itvl = BLE_GAP_SCAN_ITVL_MS(2000); // 进一步增加间隔
    coded_disc_params.window = BLE_GAP_SCAN_WIN_MS(120); // 减少扫描窗口

    // 开始扩展扫描
    // BLE_OWN_ADDR_PUBLIC：用设备的公网地址；
    // &uncoded_disc_params, &coded_disc_params：对应不同 PHY 的设置；
    // ble_cent_client_gap_event：扫描事件回调函数
    // 最后一个参数是上下文 / 用户数据，这里用 NULL。

    // 现在的做法叫“双 PHY 扫描”，NimBLE 会自动轮流扫，哪个能收到就哪个。
    rc = ble_gap_ext_disc(BLE_OWN_ADDR_PUBLIC, 0, 0, 1, 0, 0, &uncoded_disc_params,
                          &coded_disc_params, ble_cent_client_gap_event, NULL);

    // 设备开始扫描后：
    // 每发现一个广播，会触发 ble_cent_client_gap_event()；
    // 在 BLE_GAP_EVENT_EXT_DISC 类型事件中，它会解析广播数据；
    // 如果发现广播设备名是 "esp-multi-conn"，就发起连接；
    // 连接成功后再执行服务发现逻辑。
    if (rc != 0)
    {
        ESP_LOGE(TAG, "Error initiating GAP discovery procedure; rc=%d\n", rc);
    }
}

/**
 * Connects to the sender of the specified advertisement.The advertisement must contain its full
 * name which we will compare with 'BLE_PEER_NAME'.
 */
static void
ble_cent_connect(void *disc)
{
    ble_addr_t own_addr;
    ble_addr_t *peer_addr;
    struct ble_gap_multi_conn_params multi_conn_params;
    struct ble_gap_conn_params uncoded_conn_param;
    struct ble_gap_conn_params coded_conn_param;
    int rc;

    if (s_ble_multi_conn_num >= BLE_PEER_MAX_NUM)
    {
        return;
    }

#if !(MYNEWT_VAL(BLE_HOST_ALLOW_CONNECT_WITH_SCAN))
    /* Scanning must be stopped before a connection can be initiated. */
    rc = ble_gap_disc_cancel();
    if (rc != 0)
    {
        ESP_LOGE(TAG, "Failed to cancel scan; rc=%d\n", rc);
        return;
    }
#endif

    /* We won't connect to the same device. Change our static random address to simulate
     * multi-connection with only one central and one peripheral.
     */
    rc = ble_hs_id_gen_rnd(0, &own_addr);
    assert(rc == 0);
    rc = ble_hs_id_set_rnd(own_addr.val);
    assert(rc == 0);

    peer_addr = &((struct ble_gap_ext_disc_desc *)disc)->addr;

    /* The connection and scan parameters for uncoded phy (1M & 2M). */
    uncoded_conn_param.scan_itvl = BLE_GAP_SCAN_ITVL_MS(800); // 增加扫描间隔
    uncoded_conn_param.scan_window = BLE_GAP_SCAN_WIN_MS(80); // 减少扫描窗口
    uncoded_conn_param.itvl_min = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);
    uncoded_conn_param.itvl_max = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);
    uncoded_conn_param.latency = 0;
    uncoded_conn_param.supervision_timeout = BLE_GAP_SUPERVISION_TIMEOUT_MS(BLE_PREF_CONN_ITVL_MS * 30);
    uncoded_conn_param.min_ce_len = 0;
    uncoded_conn_param.max_ce_len = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);

    /* The connection and scan parameters for coded phy (125k & 500k) */
    coded_conn_param.scan_itvl = BLE_GAP_SCAN_ITVL_MS(800);  // 增加扫描间隔
    coded_conn_param.scan_window = BLE_GAP_SCAN_WIN_MS(120); // 减少扫描窗口
    coded_conn_param.itvl_min = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);
    coded_conn_param.itvl_max = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);
    coded_conn_param.latency = 0;
    coded_conn_param.supervision_timeout = BLE_GAP_SUPERVISION_TIMEOUT_MS(BLE_PREF_CONN_ITVL_MS * 30);
    coded_conn_param.min_ce_len = 0;
    coded_conn_param.max_ce_len = BLE_GAP_CONN_ITVL_MS(BLE_PREF_CONN_ITVL_MS);

    /* The parameters for multi-connect. We expect that this connection has at least
     * BLE_PREF_EVT_LEN_MS every interval to Rx and Tx.
     */
    multi_conn_params.scheduling_len_us = BLE_PREF_EVT_LEN_MS * 1000;
    multi_conn_params.own_addr_type = BLE_OWN_ADDR_RANDOM;
    multi_conn_params.peer_addr = peer_addr;
    multi_conn_params.duration_ms = 8000;
    multi_conn_params.phy_mask = BLE_GAP_LE_PHY_1M_MASK | BLE_GAP_LE_PHY_2M_MASK |
                                 BLE_GAP_LE_PHY_CODED_MASK;
    multi_conn_params.phy_1m_conn_params = &uncoded_conn_param;
    multi_conn_params.phy_2m_conn_params = &uncoded_conn_param;
    multi_conn_params.phy_coded_conn_params = &coded_conn_param;

    rc = ble_gap_multi_connect(&multi_conn_params, ble_cent_client_gap_event, NULL);

    if (rc)
    {
        ESP_LOGE(TAG, "Error: Failed to connect to device; addr_type=%d addr=%s; rc=%d\n", peer_addr->type, addr_str(peer_addr->val), rc);
    }
    else
    {
        ESP_LOGI(TAG, "Create connection. -> peer addr %s", addr_str(peer_addr->val));
    }
}

// NimBLE BLE协议栈自己出错时的“内部急救钩子”，不是系统级的重启前通知。
// 如果BLE Host出现不可恢复的错误，它会调用这个函数让你知道出了问题
static void
blecent_on_reset(int reason)
{
    ESP_LOGE(TAG, "Resetting state; reason=%d\n", reason);
}

// 协议栈启动后，会调用这个函数，可以初始化一些数据结构，比如连接管理
static void
blecent_on_sync(void)
{
    int rc;

    // 我有N个从机要连，请给每台从机给一个合理的时间片(由协议栈自行安排)
    // 为什么设置这个？在多连接场景下，连接间隔越合理，调度越精准，不容易丢包或延迟。
    rc = ble_gap_common_factor_set(true, (BLE_PREF_CONN_ITVL_MS * 1000) / 625);
    assert(rc == 0);

    ESP_LOGI(TAG, "BLE initialized with WiFi coexistence optimizations");
    ESP_LOGI(TAG, "BLE connection interval: %d ms", BLE_PREF_CONN_ITVL_MS);
    ESP_LOGI(TAG, "BLE event length: %d ms", BLE_PREF_EVT_LEN_MS);

    // 确保设备有一个合法的BLE地址（类似“身份证号”）
    // 参数为0：优先使用出厂烧写的Public地址（如果有）
    // 如果没有，就自动生成一个Random Static地址（断电后仍保持不变）(只要你不主动清除，它就保持不变)
    // 作用：BLE连接、广播都必须有地址，否则协议栈无法工作
    rc = ble_hs_util_ensure_addr(0);
    assert(rc == 0);

    // 启动扫描, 但是不发起连接
    // 真正的连接逻辑在 GAP 回调 ble_cent_client_gap_event() 中完成，
    // 通过过滤设备名是否为 BLE_PEER_NAME 来决定是否连接
    ble_cent_scan();
}

void blecent_host_task(void *param)
{
    ESP_LOGI(TAG, "BLE Host Task Started");

    // nimble_port_run() 是 阻塞调用，会一直卡在这里,直到手动调用 nimble_port_stop()，才会继续往下执行。
    // 它内部运行协议栈的主事件循环；处理所有维持连接、响应回调、分发事件
    nimble_port_run();

    // 释放掉任务占用的 FreeRTOS 资源
    nimble_port_freertos_deinit();
}

esp_err_t ble_master_init(void)
{
    int rc;

    ESP_LOGI(TAG, "Initializing BLE Master...");

    /* Initialize NVS — it is used to store PHY calibration data */
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    ret = nimble_port_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to init nimble %d ", ret);
        return ret;
    }

    /* Configure the host. */
    ble_hs_cfg.reset_cb = blecent_on_reset;
    ble_hs_cfg.sync_cb = blecent_on_sync;
    ble_hs_cfg.store_status_cb = ble_store_util_status_rr;

    /* Initialize data structures to track connected peers. */
    rc = peer_init(BLE_PEER_MAX_NUM, BLE_PEER_MAX_NUM, BLE_PEER_MAX_NUM, BLE_PEER_MAX_NUM);
    if (rc != 0)
    {
        ESP_LOGE(TAG, "Failed to initialize peer data structures");
        return ESP_FAIL;
    }

    /* Set the default device name. We will act as central only. */
    rc = ble_svc_gap_device_name_set("esp-ble-central-only");
    if (rc != 0)
    {
        ESP_LOGE(TAG, "Failed to set device name");
        return ESP_FAIL;
    }

    /* XXX Need to have template for store */
    ble_store_config_init();

    /* Initialize UART for data forwarding */
    esp_err_t uart_ret = ble_uart_init();
    if (uart_ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize UART: %s", esp_err_to_name(uart_ret));
        return uart_ret;
    }
    else
    {
        ESP_LOGI(TAG, "UART initialized successfully for BLE data forwarding");
    }

    /* Setup UART receive callback for configuration commands */
    uart_ret = ble_uart_set_recv_callback(uart_recv_callback, 100); // 100ms timeout
    if (uart_ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART receive callback: %s", esp_err_to_name(uart_ret));
        return uart_ret;
    }

    /* Start UART receive */
    uart_ret = ble_uart_start_recv();
    if (uart_ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start UART receive: %s", esp_err_to_name(uart_ret));
        return uart_ret;
    }
    else
    {
        ESP_LOGI(TAG, "UART receive started successfully for configuration commands");
    }

    ESP_LOGI(TAG, "BLE Master configuration completed, ready to start protocol stack");
    return ESP_OK;
}

esp_err_t ble_master_start(void)
{
    ESP_LOGI(TAG, "Starting BLE protocol stack...");

    // 在FreeRTOS下启动并运行BLE协议栈主线程
    // 因为协议栈是阻塞函数, 不能放在主线程中运行, 要单独开一个线程处理协议栈内部事件和状态机
    nimble_port_freertos_init(blecent_host_task);

    ESP_LOGI(TAG, "BLE protocol stack started successfully");
    return ESP_OK;
}

/**
 * @brief 写入特征值完成回调函数
 *
 * @param conn_handle 连接句柄
 * @param error       错误码 (0=成功)
 * @param attr        属性句柄
 * @param arg         用户参数
 */
static int ble_cent_write_chr_cb(uint16_t conn_handle, const struct ble_gatt_error *error,
                                 struct ble_gatt_attr *attr, void *arg)
{
    if (error->status == 0)
    {
        ESP_LOGI(TAG, "Write success to conn_handle=%d", conn_handle);
    }
    else
    {
        ESP_LOGE(TAG, "Write failed to conn_handle=%d, error=%d", conn_handle, error->status);
    }
    return 0;
}

esp_err_t ble_master_config_slave(uint16_t device_id, uint8_t sample_rate)
{
    ESP_LOGI(TAG, "Configuring slave device %d with sample rate %d Hz", device_id, sample_rate);

    if (s_ble_multi_conn_num == 0)
    {
        ESP_LOGW(TAG, "No connected slaves to configure");
        return ESP_FAIL;
    }

    // 准备配置数据
    struct gatts_slave_config config = {
        .sample_rate = sample_rate};

    bool device_found = false;

    // 遍历所有连接的设备，查找指定设备ID的从机
    for (uint16_t conn_handle = 0; conn_handle <= BLE_PEER_MAX_NUM; conn_handle++)
    {
        // 查找对应的peer来验证连接是否存在
        const struct peer *peer = peer_find(conn_handle);
        if (peer == NULL)
        {
            continue; // 该连接句柄没有对应的peer，跳过
        }

        // 首先读取从机的响应数据来获取设备ID
        const struct peer_chr *response_chr = peer_chr_find_uuid(peer, remote_svc_uuid, slave_response_char_uuid);
        if (response_chr == NULL)
        {
            ESP_LOGD(TAG, "Response characteristic not found for conn_handle=%d", conn_handle);
            continue;
        }

        // 查找配置特征值
        const struct peer_chr *config_chr = peer_chr_find_uuid(peer, remote_svc_uuid, slave_config_char_uuid);
        if (config_chr == NULL)
        {
            ESP_LOGD(TAG, "Config characteristic not found for conn_handle=%d", conn_handle);
            continue;
        }

        // 注意：这里简化处理，假设连接句柄与设备ID有某种对应关系
        // 在实际应用中，你可能需要维护一个设备ID到连接句柄的映射表
        // 或者先读取所有设备的响应数据来建立映射关系

        // 为了演示，这里假设设备ID就是连接句柄+1（你需要根据实际情况调整）
        uint16_t expected_device_id = conn_handle + 1;

        if (expected_device_id == device_id)
        {
            device_found = true;

            // 创建mbuf来存储配置数据
            struct os_mbuf *om = ble_hs_mbuf_from_flat(&config, sizeof(config));
            if (om == NULL)
            {
                ESP_LOGE(TAG, "Failed to allocate mbuf for config data");
                return ESP_FAIL;
            }

            // 写入配置特征值
            int rc = ble_gattc_write(conn_handle, config_chr->chr.val_handle, om, ble_cent_write_chr_cb, NULL);
            if (rc != 0)
            {
                ESP_LOGE(TAG, "Failed to write config to device %d (conn_handle=%d), rc=%d", device_id, conn_handle, rc);
                return ESP_FAIL;
            }

            ESP_LOGI(TAG, "Config write initiated for device %d (conn_handle=%d)", device_id, conn_handle);
            break;
        }
    }

    if (!device_found)
    {
        ESP_LOGW(TAG, "Device ID %d not found in connected slaves", device_id);
        return ESP_FAIL;
    }

    return ESP_OK;
}
