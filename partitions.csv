# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
#
# 16MB Flash 分区表优化方案
# 固件大小约1.3MB，预留增长空间到2MB
#
# 分区布局说明：
# 0x0000   - 0x8000   : 二级引导程序 (32KB)
# 0x8000   - 0x9000   : 分区表 (4KB)
# 0x9000   - 0xF000   : NVS (24KB) - WiFi配置等
# 0xF000   - 0x10000  : PHY初始化数据 (4KB)
# 0x10000  - 0x210000 : Factory 应用分区 (2MB) - 主应用分区
# 0x210000 - 0x410000 : OTA_0 应用分区 (2MB) - OTA更新分区A
# 0x410000 - 0x610000 : OTA_1 应用分区 (2MB) - OTA更新分区B
# 0x610000 - 0x612000 : OTA数据分区 (8KB)
# 0x612000 - 0x1000000: SPIFFS存储分区 (10MB) - 文件存储空间
# 总计使用: 16MB (0x1000000)

nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 0x200000,
ota_0,    app,  ota_0,   0x210000, 0x200000,
ota_1,    app,  ota_1,   0x410000, 0x200000,
otadata,  data, ota,     0x610000, 0x2000,
storage,  data, spiffs,  0x612000, 0x9EE000,
